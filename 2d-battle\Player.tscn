[gd_scene load_steps=6 format=3 uid="uid://c7d4e6f8hah0"]

[ext_resource type="Script" uid="uid://bfmr7wxrhaqi6" path="res://player_controller.gd" id="1"]
[ext_resource type="Script" uid="uid://dywd1t3m18lq6" path="res://camera_controller.gd" id="2"]
[ext_resource type="Texture2D" uid="uid://cjm2j2w68olie" path="res://icon.svg" id="2_kne1u"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(125, 119)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(136, 1)

[node name="Player" type="CharacterBody2D"]
script = ExtResource("1")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_kne1u")
offset = Vector2(0, -24)

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
position = Vector2(2.5, -25.5)
shape = SubResource("RectangleShape2D_1")

[node name="GroundCheck" type="Area2D" parent="."]
position = Vector2(0, 24)

[node name="CollisionShape2D" type="CollisionShape2D" parent="GroundCheck"]
position = Vector2(7, 12)
shape = SubResource("RectangleShape2D_2")

[node name="Camera2D" type="Camera2D" parent="."]
position = Vector2(-66, -21)
script = ExtResource("2")
