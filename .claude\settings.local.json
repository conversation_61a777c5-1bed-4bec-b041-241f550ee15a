{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(grep:*)", "Bash(find:*)", "Bash(\"/mnt/e/godot/<PERSON>ot_v4.5-beta3_win64_console.exe\":*)", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"d:/claudeCode/2d-battle\" --debug --scene \"res://scenes/camera_test_scene.tscn\")", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"d:/claudeCode/2d-battle\" --debug --scene \"res://scenes/input_test_scene.tscn\")", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"d:/claudeCode/2d-battle\" --debug --scene \"res://scenes/physics_test_scene.tscn\")", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"d:/claudeCode/2d-battle\" --debug --scene \"res://scenes/physics_test_scene.tscn\" --quit)", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://test_scene.tscn\")", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://Player.tscn\")", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://test_scene.tscn\" --quit)", "Bash(\"e:/godot/Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://Player.tscn\" --quit)", "Bash(\"E:\\godot\\Godot_v4.5-beta3_win64.exe\" --path \"D:/claudeCode/2d-battle\")", "Bash(\"E:\\godot\\Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://test_scene.tscn\")", "Bash(\"E:\\godot\\Godot_v4.5-beta3_win64_console.exe\" --path \"D:/claudeCode/2d-battle\" --debug --scene \"res://test_scene.tscn\" --quit)"], "deny": []}}