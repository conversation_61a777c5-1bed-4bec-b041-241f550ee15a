[gd_scene load_steps=5 format=3 uid="uid://b8j8k3l5m2n1o"]

[ext_resource type="PackedScene" uid="uid://c7d4e6f8g9h0" path="res://Player.tscn" id="1"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_1"]
size = Vector2(1024, 50)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_2"]
size = Vector2(200, 20)

[sub_resource type="RectangleShape2D" id="RectangleShape2D_3"]
size = Vector2(200, 20)

[node name="TestScene" type="Node2D"]

[node name="Ground" type="StaticBody2D" parent="."]
position = Vector2(512, 550)
collision_layer = 1
collision_mask = 0
groups = ["ground"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Ground"]
shape = SubResource("RectangleShape2D_1")

[node name="Platform1" type="StaticBody2D" parent="."]
position = Vector2(200, 400)
collision_layer = 1
collision_mask = 0
groups = ["ground"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform1"]
shape = SubResource("RectangleShape2D_2")

[node name="Platform2" type="StaticBody2D" parent="."]
position = Vector2(800, 300)
collision_layer = 1
collision_mask = 0
groups = ["ground"]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Platform2"]
shape = SubResource("RectangleShape2D_3")

[node name="PlayerSpawn" type="Marker2D" parent="."]
position = Vector2(100, 500)

[node name="Player" parent="PlayerSpawn" instance=ExtResource("1")]