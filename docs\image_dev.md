# 山区冒险游戏 - 图片素材开发指南

## 📋 项目概述

**游戏名称**: 2D Battle  
**游戏引擎**: Godot 4.5  
**项目目录**: 2d-battle/  
**美术风格**: 16位像素艺术 (Pixel Art) - 开发阶段使用SVG占位图  
**分辨率**: 1920x1080 (游戏画面)，精灵尺寸按像素精度设计  
**文件格式**: SVG (开发占位图) → PNG (最终像素艺术)  
**色彩方案**: 鲜艳的复古游戏配色，每个场景有独特的色调  

---

## 🎨 角色精灵设计

### 1.1 玩家角色 (Player Character)

#### 基础规格
- **尺寸**: 32x48 像素 (站立姿态)
- **方向**: 8向 (上、下、左、右、左上、右上、左下、右下)
- **动画帧**: 每个动作4-8帧
- **透明度**: 背景完全透明
- **格式**: PNG with alpha channel

#### 动作分解
| 动作类型 | 帧数 | 尺寸 | 描述 | AI提示词 |
|----------|------|------|------|----------|
| **站立** | 4帧 | 32x48 | 静止站立，轻微呼吸动画 | `pixel art game character standing idle, 32x48px, 16-bit style, blue military outfit, transparent background, game sprite` |
| **跑步** | 8帧 | 32x48 | 左右移动动画，手臂摆动 | `pixel art character running animation, 8 frames, 32x48px per frame, side view, blue military outfit, dynamic pose, transparent background` |
| **跳跃** | 6帧 | 32x48 | 跳跃上升和下降动画 | `pixel art character jumping animation, 6 frames, 32x48px, upward and downward motion, blue military, transparent background` |
| **射击** | 4帧 | 32x48 | 站立射击姿势，手臂举枪 | `pixel art character shooting, 4 frames, 32x48px, holding rifle, firing pose, blue military, transparent background` |
| **下蹲** | 4帧 | 32x48 | 下蹲姿态，可射击 | `pixel art character crouching, 4 frames, 32x48px, low profile, blue military outfit, transparent background` |
| **受伤** | 3帧 | 32x48 | 受伤闪烁效果 | `pixel art character hurt, 3 frames, 32x48px, damage flash effect, red overlay, transparent background` |
| **死亡** | 6帧 | 32x48 | 倒地死亡动画 | `pixel art character death animation, 6 frames, 32x48px, falling down, disappearing effect, transparent background` |

#### 色彩方案
- **主色调**: 蓝色军事服装 (#4A90E2)
- **次要色**: 棕色装备 (#8B4513)
- **肤色**: 浅色 (#FDBCB4)
- **头发**: 深棕色 (#654321)

### 1.2 敌人角色设计

#### 1.2.1 山区敌人

**山地守卫 (Mountain Guard)**
- **尺寸**: 32x40 像素
- **动画**: 站立(4帧)、巡逻(8帧)、射击(4帧)、死亡(4帧)
- **AI提示词**: `pixel art enemy soldier, mountain guard, 32x40px, green military uniform, holding rifle, patrol animation, transparent background`

**狙击手 (Sniper)**
- **尺寸**: 32x48 像素
- **动画**: 蹲伏(4帧)、瞄准(4帧)、射击(2帧)、死亡(4帧)
- **AI提示词**: `pixel art sniper enemy, 32x48px, camouflage outfit, holding sniper rifle, crouching position, transparent background`

**爆炸兵 (Bomber)**
- **尺寸**: 28x36 像素
- **动画**: 走路(6帧)、准备爆炸(4帧)、爆炸(8帧)
- **AI提示词**: `pixel art suicide bomber enemy, 28x36px, red vest, holding dynamite, walking animation, transparent background`

#### 1.2.2 山洞敌人

**蝙蝠 (Bat)**
- **尺寸**: 24x24 像素
- **动画**: 飞行(4帧)、攻击(2帧)、死亡(3帧)
- **AI提示词**: `pixel art bat enemy, 24x24px, flying animation, dark purple, cave creature, transparent background`

**洞穴蜘蛛 (Cave Spider)**
- **尺寸**: 32x32 像素
- **动画**: 爬行(6帧)、跳跃(4帧)、死亡(4帧)
- **AI提示词**: `pixel art giant spider, 32x32px, cave spider, crawling animation, multiple legs, transparent background`

**地精 (Goblin)**
- **尺寸**: 24x32 像素
- **动画**: 跑动(8帧)、攻击(4帧)、死亡(4帧)
- **AI提示词**: `pixel art goblin enemy, 24x32px, green skin, primitive weapon, running animation, transparent background`

#### 1.2.3 火车敌人

**火车守卫 (Train Guard)**
- **尺寸**: 32x44 像素
- **动画**: 站立(4帧)、巡逻(6帧)、射击(4帧)、死亡(4帧)
- **AI提示词**: `pixel art train guard, 32x44px, industrial uniform, mechanical armor, holding rifle, transparent background`

**机械臂 (Mechanical Arm)**
- **尺寸**: 48x64 像素
- **动画**: 待机(4帧)、攻击(6帧)、损坏(4帧)
- **AI提示词**: `pixel art mechanical arm, 48x64px, steampunk style, robotic arm, industrial design, transparent background`

**火车头Boss (Train Boss)**
- **尺寸**: 96x128 像素
- **动画**: 待机(8帧)、移动(8帧)、攻击(12帧)、受伤(4帧)、死亡(16帧)
- **AI提示词**: `pixel art train boss, 96x128px, steam locomotive with face, mechanical monster, boss battle, transparent background`

#### 1.2.4 沙漠敌人

**沙盗 (Desert Bandit)**
- **尺寸**: 32x40 像素
- **动画**: 站立(4帧)、跑动(8帧)、射击(4帧)、死亡(4帧)
- **AI提示词**: `pixel art desert bandit, 32x40px, desert clothing, headscarf, holding pistol, transparent background`

**沙虫 (Sand Worm)**
- **尺寸**: 64x48 像素
- **动画**: 潜伏(4帧)、突袭(8帧)、受伤(4帧)、死亡(6帧)
- **AI提示词**: `pixel art sand worm, 64x48px, giant desert worm, emerging from sand, attack animation, transparent background`

**哨兵塔 (Sentinel Tower)**
- **尺寸**: 48x80 像素
- **动画**: 待机(4帧)、瞄准(4帧)、射击(4帧)、摧毁(6帧)
- **AI提示词**: `pixel art sentinel tower, 48x80px, automated turret, desert watchtower, mechanical design, transparent background`

---

## 🌄 场景背景设计

### 2.1 背景规格
- **分辨率**: 1920x1080 像素
- **格式**: PNG (可选择透明背景或纯色背景)
- **层次**: 多层背景 (远景、中景、近景)
- **风格**: 像素艺术，可轻微模糊以营造景深效果

### 2.2 场景背景列表

#### 山区背景 (Mountain Background)
**AI提示词**: `pixel art mountain landscape, 1920x1080px, green mountains, blue sky, white clouds, pine trees, waterfall, peaceful scene, game background`

**分层要求**:
- **远景层**: 山脉轮廓，天空，云朵
- **中景层**: 山坡，松树，瀑布
- **近景层**: 前景岩石，草地

#### 山洞背景 (Cave Background)
**AI提示词**: `pixel art cave interior, 1920x1080px, dark cave, stalactites, stalagmites, underground river, crystals, dim lighting, game background`

**分层要求**:
- **远景层**: 洞穴深处，黑暗区域
- **中景层**: 钟乳石，石笋，地下河
- **近景层**: 洞穴入口，岩石

#### 火车背景 (Train Background)
**AI提示词**: `pixel art train yard, 1920x1080px, steam trains, industrial area, railway tracks, smoke stacks, machinery, sunset lighting, game background`

**分层要求**:
- **远景层**: 工业建筑，烟囱
- **中景层**: 火车，轨道，煤炭堆
- **近景层**: 机械零件，铁轨

#### 沙漠背景 (Desert Background)
**AI提示词**: `pixel art desert landscape, 1920x1080px, golden sand dunes, sunset, ancient ruins, palm trees, oasis, dramatic lighting, game background`

**分层要求**:
- **远景层**: 远山，日落天空
- **中景层**: 沙丘，废墟建筑
- **近景层**: 前景沙地，岩石

---

## 🎮 UI界面设计

### 3.1 UI规格
- **分辨率**: 1920x1080 像素
- **格式**: PNG with alpha channel
- **风格**: 像素艺术UI，复古游戏界面风格
- **字体**: 像素字体，清晰易读

### 3.2 开始界面 (Start Screen)
**AI提示词**: `pixel art game start screen, 1920x1080px, mountain adventure title, retro game UI, start button, name input field, instructions button, quit button, pixel font, transparent background`

**组件要求**:
- 游戏标题Logo (256x128 像素)
- 开始按钮 (200x60 像素)
- 名称输入框 (300x50 像素)
- 操作说明按钮 (200x60 像素)
- 退出按钮 (200x60 像素)

### 3.3 HUD界面 (Heads-Up Display)
**AI提示词**: `pixel art game HUD, retro game interface, health hearts, score display, timer, ammo counter, mini-map, transparent background, game UI`

**组件要求**:
- 生命值显示 (心形图标，32x32 像素)
- 分数显示区域 (200x40 像素)
- 时间显示区域 (150x40 像素)
- 弹药计数区域 (150x40 像素)
- 小地图区域 (200x200 像素)

### 3.4 结束界面 (End Screen)
**AI提示词**: `pixel art game over screen, victory and defeat variants, score display, time elapsed, player rating, restart button, main menu button, retro game UI, transparent background`

**组件要求**:
- 胜利/失败标题 (300x80 像素)
- 分数显示区域 (300x60 像素)
- 时间显示区域 (300x40 像素)
- 评级显示 (200x60 像素)
- 重新开始按钮 (200x60 像素)
- 主菜单按钮 (200x60 像素)

### 3.5 按钮设计
**按钮状态**:
- **正常状态**: 标准颜色
- **悬停状态**: 高亮颜色
- **点击状态**: 按下效果

**AI提示词**: `pixel art game button, 200x60px, retro style, normal/hover/pressed states, beveled edge, pixel font, transparent background`

---

## 🎁 道具与特效

### 4.1 道具设计
**规格**: 32x32 像素，PNG透明背景

#### 生命值道具
**AI提示词**: `pixel art heart health pickup, 32x32px, red heart, glowing effect, medical cross, transparent background, game item`

#### 弹药道具
**AI提示词**: `pixel art ammo pickup, 32x32px, bullet box, ammunition, military style, transparent background, game item`

#### 特殊武器
**AI提示词**: `pixel art flamethrower pickup, 32x32px, fire weapon, special power-up, orange flames, transparent background, game item`

#### 分数道具
**AI提示词**: `pixel art coin pickup, 32x32px, golden coin, treasure, sparkling effect, transparent background, game item`

### 4.2 特效动画

#### 爆炸特效
**AI提示词**: `pixel art explosion animation, 8 frames, 64x64px per frame, fire explosion, smoke, debris, transparent background, game effect`

#### 射击特效
**AI提示词**: `pixel art muzzle flash, 4 frames, 32x32px, gun fire effect, yellow-orange flash, transparent background, game effect`

#### 环境特效
**瀑布**: `pixel art waterfall animation, 8 frames, 128x256px, flowing water, blue water, transparent background`
**蒸汽**: `pixel art steam effect, 6 frames, 64x64px, industrial steam, white smoke, transparent background`
**风沙**: `pixel art sandstorm effect, 8 frames, 128x128px, blowing sand, brown particles, transparent background`

---

## 🎨 图标与装饰元素

### 5.1 图标设计
**规格**: 32x32 像素，PNG透明背景

**功能图标**:
- 设置图标: `pixel art settings icon, 32x32px, gear, configuration, transparent background`
- 音量图标: `pixel art volume icon, 32x32px, speaker, audio control, transparent background`
- 暂停图标: `pixel art pause icon, 32x32px, two vertical bars, transparent background`

### 5.2 装饰元素
**地形装饰**:
- 岩石: `pixel art rock, 64x32px, stone terrain, environment decoration, transparent background`
- 草丛: `pixel art grass bush, 48x32px, green vegetation, environment decoration, transparent background`
- 宝箱: `pixel art treasure chest, 48x32px, wooden chest, gold coins, transparent background`

---

## 📁 当前项目文件结构

```
2d-battle/
├── project.godot                 # Godot项目配置文件
├── icon.svg                      # 游戏图标
├── assets/                       # 资源文件夹
│   ├── characters/               # 角色资源
│   │   └── player/               # 玩家角色
│   │       ├── player_idle_placeholder.svg
│   │       └── player_idle_placeholder.svg.import
│   └── items/                    # 道具物品
│       ├── health_heart_placeholder.svg
│       ├── health_heart_placeholder.svg.import
│       ├── ammo_box_placeholder.svg
│       ├── ammo_box_placeholder.svg.import
│       ├── coin_gold_placeholder.svg
│       ├── coin_gold_placeholder.svg.import
│       ├── flamethrower_placeholder.svg
│       └── flamethrower_placeholder.svg.import
```

### 📁 计划文件结构 (最终目标)

```
2d-battle/assets/
├── /characters
│   ├── /player
│   │   ├── player_idle.png
│   │   ├── player_run.png
│   │   ├── player_jump.png
│   │   ├── player_shoot.png
│   │   ├── player_crouch.png
│   │   ├── player_hurt.png
│   │   └── player_death.png
│   ├── /enemies
│   │   ├── /mountain
│   │   │   ├── mountain_guard.png
│   │   │   ├── sniper.png
│   │   │   └── bomber.png
│   │   ├── /cave
│   │   │   ├── bat.png
│   │   │   ├── spider.png
│   │   │   └── goblin.png
│   │   ├── /train
│   │   │   ├── train_guard.png
│   │   │   ├── mechanical_arm.png
│   │   │   └── train_boss.png
│   │   └── /desert
│   │       ├── desert_bandit.png
│   │       ├── sand_worm.png
│   │       └── sentinel_tower.png
├── /backgrounds
│   ├── mountain_bg_far.png
│   ├── mountain_bg_mid.png
│   ├── mountain_bg_near.png
│   ├── cave_bg_far.png
│   ├── cave_bg_mid.png
│   ├── cave_bg_near.png
│   ├── train_bg_far.png
│   ├── train_bg_mid.png
│   ├── train_bg_near.png
│   ├── desert_bg_far.png
│   ├── desert_bg_mid.png
│   └── desert_bg_near.png
├── /ui
│   ├── start_screen.png
│   ├── hud_elements.png
│   ├── end_screen_victory.png
│   ├── end_screen_defeat.png
│   ├── buttons_normal.png
│   ├── buttons_hover.png
│   └── buttons_pressed.png
├── /items
│   ├── health_heart.png
│   ├── ammo_box.png
│   ├── flamethrower.png
│   ├── coin_gold.png
│   └── treasure_chest.png
├── /effects
│   ├── explosion.png
│   ├── muzzle_flash.png
│   ├── waterfall.png
│   ├── steam.png
│   └── sandstorm.png
└── /icons
    ├── icon_settings.png
    ├── icon_volume.png
    ├── icon_pause.png
    ├── rock_decoration.png
    ├── grass_bush.png
    └── treasure_chest.png
```

---

## 🎨 色彩规范

### 主色调
- **山区**: 绿色 (#4CAF50)、蓝色 (#2196F3)、棕色 (#795548)
- **山洞**: 深蓝 (#1565C0)、紫色 (#7B1FA2)、灰色 (#607D8B)
- **火车**: 橙色 (#FF5722)、灰色 (#9E9E9E)、黑色 (#424242)
- **沙漠**: 金色 (#FFC107)、橙色 (#FF9800)、棕色 (#8D6E63)

### UI色彩
- **主要按钮**: 蓝色 (#2196F3)
- **次要按钮**: 绿色 (#4CAF50)
- **危险按钮**: 红色 (#F44336)
- **文字**: 白色 (#FFFFFF)
- **背景**: 半透明黑色 (rgba(0,0,0,0.8))

---

## 🔧 技术要求

### 文件格式
- **精灵动画**: PNG序列，每帧独立文件
- **背景图片**: PNG，支持透明通道
- **UI元素**: PNG，透明背景
- **图标**: PNG，透明背景

### 分辨率标准
- **角色精灵**: 24-128 像素 (根据角色大小)
- **道具物品**: 32x32 像素
- **UI元素**: 根据界面需求定制
- **背景图片**: 1920x1080 像素

### 透明度要求
- 所有精灵必须具有完全透明的背景
- UI元素支持半透明效果
- 特效支持渐变透明效果

### 命名规范
- 使用小写字母和下划线
- 描述性名称，易于理解
- 包含动画状态信息
- 示例: `player_run_01.png`, `explosion_08.png`

---

## 📝 质量检查清单

### 精灵动画检查
- [ ] 所有动画帧尺寸一致
- [ ] 背景完全透明
- [ ] 动画流畅自然
- [ ] 像素对齐准确
- [ ] 色彩符合场景主题

### UI界面检查
- [ ] 文字清晰可读
- [ ] 按钮状态完整
- [ ] 布局合理美观
- [ ] 风格统一一致
- [ ] 响应区域准确

### 背景图片检查
- [ ] 分层效果正确
- [ ] 色彩协调统一
- [ ] 无明显接缝
- [ ] 细节丰富适当
- [ ] 性能优化良好

---

**文档版本**: v2.0  
**创建日期**: 2025-08-04  
**最后更新**: 2025-08-04  
**美术负责人**: UI美工  
**项目状态**: 开发阶段 - 占位图完成，准备开始核心功能开发  

*注: 本文档将根据项目进展和美术需求进行动态更新。*

---

## 🎯 美工完成状态

### 已生成的占位图像

#### ✅ 玩家角色占位图
- **文件**: `2d-battle/assets/characters/player/player_idle_placeholder.svg`
- **尺寸**: 32x48 像素
- **描述**: 玩家角色站立姿态占位图，蓝色军事服装，持步枪
- **状态**: 已完成并导入Godot项目

#### ✅ 道具占位图
1. **生命值道具**: `2d-battle/assets/items/health_heart_placeholder.svg`
   - 尺寸: 32x32 像素
   - 描述: 红色心形，带医疗十字标记
   - 状态: 已完成并导入Godot项目

2. **弹药道具**: `2d-battle/assets/items/ammo_box_placeholder.svg`
   - 尺寸: 32x32 像素
   - 描述: 棕色弹药箱，金色子弹
   - 状态: 已完成并导入Godot项目

3. **火焰喷射器**: `2d-battle/assets/items/flamethrower_placeholder.svg`
   - 尺寸: 32x32 像素
   - 描述: 橙色火焰喷射器，带火焰效果
   - 状态: 已完成并导入Godot项目

4. **金币道具**: `2d-battle/assets/items/coin_gold_placeholder.svg`
   - 尺寸: 32x32 像素
   - 描述: 金色硬币，带$符号和闪光效果
   - 状态: 已完成并导入Godot项目

### 占位图特点
- **格式**: SVG矢量格式，可无损缩放
- **风格**: 简洁的几何形状，便于开发阶段使用
- **颜色**: 按照最终色彩方案设计
- **透明度**: 支持透明背景，符合游戏要求
- **用途**: 开发阶段的功能测试和界面布局

### 后续优化计划
- [ ] 将SVG占位图转换为像素艺术PNG格式
- [ ] 添加详细的动画帧序列
- [ ] 增加阴影和高光效果
- [ ] 优化像素对齐和细节
- [ ] 创建完整的精灵图集