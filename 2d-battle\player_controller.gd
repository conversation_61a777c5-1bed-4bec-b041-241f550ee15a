extends CharacterBody2D

@onready var ground_check = $GroundCheck
@onready var sprite = $Sprite2D

const SPEED = 300.0
const JUMP_VELOCITY = -400.0
const GRAVITY = 980.0

var is_grounded = false

func _ready():
	ground_check.body_entered.connect(_on_ground_entered)
	ground_check.body_exited.connect(_on_ground_exited)

func _physics_process(delta):
	if not is_on_floor():
		velocity.y += GRAVITY * delta
	
	if Input.is_action_just_pressed("jump") and is_on_floor():
		velocity.y = JUMP_VELOCITY
	
	var direction = Input.get_axis("move_left", "move_right")
	if direction != 0:
		velocity.x = direction * SPEED
		sprite.flip_h = direction < 0
	else:
		velocity.x = move_toward(velocity.x, 0, SPEED)
	
	move_and_slide()

func _on_ground_entered(body):
	if body.is_in_group("ground"):
		is_grounded = true

func _on_ground_exited(body):
	if body.is_in_group("ground"):
		is_grounded = false
