# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Project Name**: 2D Battle  
**Game Engine**: Godot 4.5  
**Project Directory**: 2d-battle/  
**Development Environment**: Windows 
**Current Status**: Project reset, starting scene-focused development

## Environment Configuration

### Godot Engine
- **Executable**: `E:\godot\Godot_v4.5-beta3_win64.exe`
- **Version**: Godot 4.5 beta3
- **Platform**: Windows 

### Project Structure
```
2d-battle/                    # Main project directory
├── project.godot             # Godot project configuration
├── icon.svg                  # Game icon
├── assets/                   # Resource folder
│   ├── characters/           # Character sprites
│   │   └── player/          # Player character assets
│   └── items/               # Game items and pickups
```

### Asset Status
- ✅ Player character placeholder (SVG) - imported to Godot
- ✅ Item placeholders (4x SVG) - health, ammo, flamethrower, coin
- ✅ All assets have .import files generated by Godot

## Development Commands

### Godot Commands
```bash

"e:/godot/Godot_v4.5-beta3_win64.exe" --path "d：/claudeCode/2d-battle"

# Export project (when ready)
"e:/godot/Godot_v4.5-beta3_win64.exe" --export "Windows Desktop" --path "d:/claudeCode/2d-battle"
```

### Working Directory
```bash
# Navigate to project directory
cd d:/claudeCode/2d-battle

# View project structure
ls -la
```

## Architecture

### Current Architecture
- **Game Type**: 2D side-scrolling battle game
- **Rendering**: 2D mobile renderer (configured in project.godot)
- **Target Platform**: Windows (primary), with cross-platform considerations
- **Asset Pipeline**: SVG placeholders → PNG pixel art final assets

### Development Approach
**Scene-Focused Development**: Building playable scenes first, then refining systems
- **Scene 1**: Basic test scene (player movement, physics)
- **Scene 2**: Mountain scene (basic enemies, shooting)
- **Scene 3**: Cave scene (flying enemies, traps)
- **Scene 4**: Train scene (mechanical enemies, moving platforms)
- **Scene 5**: Desert boss scene (boss battles, special weapons)

### Development Timeline
- **Development Cycle**: 12 weeks total
- **Current Focus**: Scene 1 - Basic test scene
- **Team Size**: 3 members (Development Expert, UI Artist, Tester)

## Team Roles

### Development Expert
- Focus: Scene development, game logic, physics, AI, architecture
- Current task: Scene 1 - Basic test scene development
- Tools: Godot editor, GDScript, 2D physics engine
- Responsibilities: Create playable scenes, implement core mechanics

### UI Artist  
- Focus: Scene art assets, animations, visual effects, pixel art conversion
- Current task: Converting SVG placeholders to pixel art for Scene 1
- Tools: Image generation, SVG editing, pixel art software
- Responsibilities: Create scene-specific art assets and animations

### Tester
- Focus: Scene testing, quality assurance, bug reporting, user experience
- Current task: Preparing test plans for Scene 1 functionality
- Tools: Godot testing, performance monitoring
- Responsibilities: Test each scene for functionality, performance, and playability

## File Conventions

### Asset Naming
- Use lowercase with underscores
- Placeholder assets end with `_placeholder.svg`
- Final assets use `.png` format
- Example: `player_idle_placeholder.svg` → `player_idle.png`

### Scene Organization
- Main scenes in root directory
- Resource files in `assets/` subdirectories
- Import files automatically generated by Godot

### Script Organization
- GDScript files alongside corresponding scenes
- Use PascalCase for class names
- Use snake_case for variables and functions

## Memory & Performance

### Current Considerations
- SVG placeholders provide scalable development assets
- Mobile renderer configured for optimal 2D performance
- Asset importing handled automatically by Godot

### Future Optimizations
- Convert SVG to PNG for final release
- Implement object pooling for bullets and effects
- Optimize sprite sheets for better memory usage

## Known Issues & Limitations

### Physics System Limitations
**Godot 4.5 Beta 3 Compatibility Issues:**

The following physics query parameter classes are not available in the current Godot 4.5 beta3 version:
- `PhysicsRayCastQueryParameters2D` 
- `PhysicsShapeCastQueryParameters2D`

**Affected Functions in `physics_manager.gd`:**
- `ray_cast()` - Currently using RayCast2D node as temporary workaround
- `shape_cast()` - Currently using ShapeCast2D node as temporary workaround  
- `get_nodes_in_area()` - Currently using Area2D node as temporary workaround

**Temporary Solutions:**
- All functions have been reimplemented using corresponding 2D nodes
- Original implementations are commented out with TODO markers
- Performance impact: Temporary nodes are created and destroyed during physics queries

**Future Action Required:**
- Re-enable the original implementations when these classes become available in stable Godot 4.5
- The commented code should be uncommented and temporary node-based solutions removed