extends Camera2D

@export var follow_target: Node2D
@export var smoothing: float = 5.0
@export var camera_offset: Vector2 = Vector2.ZERO

func _ready():
	if follow_target:
		global_position = follow_target.global_position + camera_offset

func _physics_process(delta):
	if follow_target:
		var target_pos = follow_target.global_position + camera_offset
		global_position = global_position.lerp(target_pos, smoothing * delta)
