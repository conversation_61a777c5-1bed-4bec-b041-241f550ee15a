# 山区冒险 - 2D横版射击游戏 PRD

## 项目概述

**游戏名称**: 山区冒险 (Mountain Adventure)  
**游戏类型**: 2D横版射击游戏  
**目标平台**: PC (Windows/Mac/Linux)  
**游戏引擎**: Godot 4.4.1  
**开发语言**: GDScript  
**游戏风格**: 类似魂斗罗的动作射击游戏，包含跳跃、射击、平台解谜元素

## 游戏特色

- **多样化场景**: 山区、山洞、火车、沙漠四个独特场景
- **流畅操作**: 精确的跳跃和射击控制
- **进度记录**: 完整的游戏数据追踪系统
- **用户个性化**: 玩家名称输入和成绩记录
- **完整游戏循环**: 开始界面、游戏过程、结束界面、重启功能

## 游戏机制

### 核心玩法

1. **角色控制**
   - 左右移动 (A/D 或 方向键)
   - 跳跃 (W 或 空格键)
   - 射击 (鼠标左键或 J 键)
   - 下蹲 (S 或 下方向键)

2. **角色属性**
   - 生命值: 3点
   - 射击方式: 单发连射
   - 移动速度: 中等
   - 跳跃高度: 标准平台跳跃高度

3. **游戏目标**
   - 穿越四个场景区域
   - 消灭敌人
   - 收集道具
   - 到达终点

### 敌人设计

1. **山区敌人**
   - 山地守卫: 基础步兵，持枪射击
   - 狙击手: 远程攻击，高伤害
   - 爆炸兵: 近身自爆攻击

2. **山洞敌人**
   - 蝙蝠: 飞行敌人，快速移动
   - 洞穴蜘蛛: 垂直移动的陷阱敌人
   - 地精: 小型快速敌人

3. **火车敌人**
   - 火车守卫: 在车厢顶部巡逻
   - 机械臂: 固定位置的自动攻击装置
   - 火车头Boss: 关卡Boss

4. **沙漠敌人**
   - 沙盗: 埋伏式攻击
   - 沙虫: 从地下突袭
   - 哨兵塔: 固定炮台

## 场景设计

### 第一关: 山区冒险

#### 1. 山区起始区域
- **视觉风格**: 绿色山脉、蓝天白云、松树
- **地形特征**: 
  - 多层平台
  - 悬崖边缘
  - 木制桥梁
  - 瀑布背景
- **游戏元素**:
  - 教程提示
  - 基础敌人
  - 生命值道具
  - 弹药补给

#### 2. 山洞区域
- **视觉风格**: 昏暗洞穴、钟乳石、地下水
- **地形特征**:
  - 狭窄通道
  - 深坑
  - 悬浮平台
  - 地下河流
- **游戏元素**:
  - 光照变化
  - 回声效果
  - 隐藏宝藏
  - 环境危害

#### 3. 火车区域
- **视觉风格**: 蒸汽火车、工业风格、机械元素
- **地形特征**:
  - 移动的火车车厢
  - 煤炭堆
  - 机械装置
  - 隧道入口
- **游戏元素**:
  - 移动平台
  - Boss战
  - 机械陷阱
  - 速度变化

#### 4. 沙漠终点区域
- **视觉风格**: 金色沙丘、日落景象、废墟
- **地形特征**:
  - 沙丘起伏
  - 废弃建筑
  - 风沙效果
  - 远山背景
- **游戏元素**:
  - 最终Boss
  - 结束触发点
  - 隐藏道具
  - 胜利动画

## UI/UX设计

### 1. 开始界面
- **主要元素**:
  - 游戏标题和Logo
  - 玩家名称输入框
  - 开始游戏按钮
  - 操作说明按钮
  - 退出游戏按钮
- **背景**: 动态的游戏场景预览
- **音效**: 激昂的主题音乐

### 2. 游戏界面
- **HUD元素**:
  - 生命值显示 (心形图标)
  - 分数显示
  - 游戏时间
  - 弹药数量
  - 小地图 (可选)
- **位置**: 屏幕顶部和底部

### 3. 暂停菜单
- **选项**:
  - 继续游戏
  - 重新开始
  - 返回主菜单
  - 退出游戏
- **背景**: 游戏画面暂停

### 4. 结束界面
- **胜利条件**:
  - 显示最终分数
  - 游戏耗时
  - 玩家名称
  - 评级系统
- **功能按钮**:
  - 重新开始
  - 返回主菜单
  - 退出游戏
- **失败条件**:
  - 显示失败原因
  - 重试选项

## 技术要求

### Godot 4.4.1 特性
- **节点系统**: 使用Scene Tree进行游戏对象管理
- **物理引擎**: 2D物理引擎用于碰撞检测和重力
- **动画系统**: AnimationPlayer和AnimatedSprite2D
- **脚本系统**: GDScript进行游戏逻辑编程
- **音频系统**: AudioStreamPlayer处理音效和音乐

### 核心系统架构
1. **GameManager**: 全局游戏状态管理
2. **PlayerController**: 玩家输入和移动控制
3. **EnemyAI**: 敌人行为逻辑
4. **LevelManager**: 场景加载和进度管理
5. **UIManager**: 界面显示和交互
6. **SaveSystem**: 游戏数据保存和读取

## 数据追踪系统

### 游戏数据记录
- **玩家信息**: 名称、游戏ID
- **时间记录**: 开始时间、结束时间、总耗时
- **分数系统**: 
  - 击败敌人: 100-500分
  - 收集道具: 50-200分
  - 时间奖励: 剩余时间×10分
  - 完成奖励: 1000分
- **统计信息**: 击败敌人数、死亡次数、道具收集数

### 数据持久化
- **保存格式**: JSON文件
- **保存位置**: 用户本地目录
- **保存时机**: 游戏结束时自动保存
- **读取时机**: 主界面显示历史记录

## 开发里程碑

### 阶段1: 基础框架 (2周)
- [ ] 项目初始化和基础设置
- [ ] 玩家角色控制器开发
- [ ] 基础物理系统实现
- [ ] 简单场景搭建

### 阶段2: 核心玩法 (3周)
- [ ] 射击系统完善
- [ ] 敌人AI开发
- [ ] 碰撞检测系统
- [ ] 道具系统实现

### 阶段3: 场景制作 (4周)
- [ ] 山区场景设计和实现
- [ ] 山洞场景开发
- [ ] 火车场景制作
- [ ] 沙漠场景完成

### 阶段4: UI系统 (2周)
- [ ] 开始界面开发
- [ ] HUD界面实现
- [ ] 结束界面制作
- [ ] 数据保存系统

### 阶段5: 测试优化 (2周)
- [ ] 游戏平衡性调整
- [ ] 性能优化
- [ ] Bug修复
- [ ] 最终测试

## 资源需求

### 图形资源
- **角色精灵**: 玩家角色、敌人、NPC
- **场景贴图**: 背景、地形、道具
- **UI素材**: 按钮、图标、字体
- **特效**: 爆炸、射击、环境效果

### 音频资源
- **背景音乐**: 主题音乐、场景音乐
- **音效**: 射击、爆炸、跳跃、受伤
- **语音**: 角色语音、提示音

### 技术资源
- **Godot 4.4.1**: 游戏引擎
- **开发工具**: 代码编辑器、图像处理软件
- **版本控制**: Git仓库管理

## 风险评估

### 技术风险
- **性能问题**: 复杂场景可能导致帧率下降
- **兼容性**: 不同平台的适配问题
- **Bug修复**: 游戏逻辑错误的排查

### 设计风险
- **游戏平衡**: 难度曲线调整
- **用户体验**: 操作手感优化
- **内容完整性**: 场景和功能完整性

### 进度风险
- **开发延期**: 功能实现时间预估
- **资源准备**: 素材制作进度
- **测试时间**: 充分的测试周期

## 成功标准

### 质量标准
- **帧率稳定**: 60FPS流畅运行
- **无重大Bug**: 游戏可正常完成
- **操作流畅**: 响应迅速的控制体验
- **视觉完整**: 统一的艺术风格

### 功能标准
- **完整游戏循环**: 从开始到结束的完整体验
- **数据记录**: 准确的游戏数据保存
- **重启功能**: 流畅的游戏重启体验
- **用户输入**: 完整的名称输入和数据记录

### 用户体验标准
- **易于上手**: 简单直观的操作
- **有趣味性**: 富有挑战性的游戏体验
- **成就感**: 完成游戏的满足感
- **重玩价值**: 不同的游戏策略和路线

## 附录

### 控制方案
```
移动: A/D 或 左右方向键
跳跃: W 或 空格键
射击: 鼠标左键 或 J 键
下蹲: S 或 下方向键
暂停: ESC 或 P 键
```

### 技术规格
- **分辨率**: 1920x1080 (支持缩放)
- **帧率**: 60 FPS
- **文件大小**: 目标 < 100MB
- **支持平台**: Windows, Mac, Linux

### 参考游戏
- 魂斗罗 (Contra) 系列
- 金属_slug (Metal Slug) 系列
- 超级马里奥 (Super Mario) 系列